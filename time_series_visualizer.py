import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from pandas.plotting import register_matplotlib_converters
register_matplotlib_converters()

# 1 º passo: Carrega os dados do arquivo CSV e define a coluna 'date' como índice
# parse_dates=True converte automaticamente a coluna de data para datetime
df = pd.read_csv('fcc-forum-pageviews.csv', parse_dates=['date'], index_col='date')

# 2 º passo: Clean data
# Remove outliers: filtra dados que estão fora dos percentis 2.5% e 97.5%
# Isso elimina os valores extremos que podem distorcer a visualização
df = df[
    (df['value'] >= df['value'].quantile(0.025)) &
    (df['value'] <= df['value'].quantile(0.975))
]


def draw_line_plot():
    # 3 º passo: Draw line plot
    # Cria uma cópia dos dados para evitar modificações no DataFrame original
    df_line = df.copy()

    # 4 º passo: Cria a figura e o eixo para o gráfico de linha
    fig, ax = plt.subplots(figsize=(15, 5))

    # 5 º passo: Plota o gráfico de linha com as visualizações de página ao longo do tempo
    ax.plot(df_line.index, df_line['value'], color='red', linewidth=1)

    # Define o título e rótulos dos eixos conforme especificado
    ax.set_title('Daily freeCodeCamp Forum Page Views 5/2016-12/2019')
    ax.set_xlabel('Date')
    ax.set_ylabel('Page Views')

    # Melhora a formatação das datas no eixo x
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 6 º passo: Save image and return fig (don't change this part)
    fig.savefig('line_plot.png')
    return fig

def draw_bar_plot():
    # 7 º passo: Copy and modify data for monthly bar plot
    # Cria uma cópia dos dados e adiciona colunas para ano e mês
    df_bar = df.copy()
    df_bar['year'] = df_bar.index.year
    df_bar['month'] = df_bar.index.month

    # 8 º passo: Calcula a média de visualizações por mês e ano
    # Agrupa por ano e mês, depois calcula a média das visualizações
    df_bar_grouped = df_bar.groupby(['year', 'month'])['value'].mean().unstack()

    # 9 º passo: Draw bar plot
    # Cria a figura para o gráfico de barras
    fig, ax = plt.subplots(figsize=(12, 8))

    # 10 º passo: Cria o gráfico de barras com os dados agrupados
    # Cada ano será um grupo de barras, cada mês será uma barra dentro do grupo
    df_bar_grouped.plot(kind='bar', ax=ax, width=0.8)

    # Define os rótulos dos eixos conforme especificado
    ax.set_xlabel('Years')
    ax.set_ylabel('Average Page Views')

    # Configura a legenda com os nomes dos meses
    month_names = ['January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December']
    ax.legend(month_names, title='Months', loc='upper left')

    # Melhora a formatação do gráfico
    plt.xticks(rotation=0)
    plt.tight_layout()

    # 11 º passo: Save image and return fig (don't change this part)
    fig.savefig('bar_plot.png')
    return fig

def draw_box_plot():
    # 12 º passo: Prepare data for box plots (this part is done!)
    df_box = df.copy()
    df_box.reset_index(inplace=True)
    df_box['year'] = [d.year for d in df_box.date]
    df_box['month'] = [d.strftime('%b') for d in df_box.date]

    # 13 º passo: Draw box plots (using Seaborn)
    # Cria uma figura com dois subplots lado a lado
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Primeiro gráfico de caixa: distribuição por ano (tendência)
    sns.boxplot(x='year', y='value', data=df_box, ax=ax1)
    ax1.set_title('Year-wise Box Plot (Trend)')
    ax1.set_xlabel('Year')
    ax1.set_ylabel('Page Views')

    # Segundo gráfico de caixa: distribuição por mês (sazonalidade)
    # Define a ordem dos meses para exibição correta
    month_order = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    sns.boxplot(x='month', y='value', data=df_box, order=month_order, ax=ax2)
    ax2.set_title('Month-wise Box Plot (Seasonality)')
    ax2.set_xlabel('Month')
    ax2.set_ylabel('Page Views')

    # Ajusta o layout para evitar sobreposição
    plt.tight_layout()

    # 14 º passo: Save image and return fig (don't change this part)
    fig.savefig('box_plot.png')
    return fig
